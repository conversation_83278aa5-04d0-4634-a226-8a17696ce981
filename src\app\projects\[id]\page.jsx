import BuildPageComponent from '@/components/BuildingPage/BuildPageComponent'
import ScrollerWrapper from '@/components/BuildingPage/ScrollerWrapper'
import ExperienceWrapper from '@/components/experience/ExperienceWrapper'
import { settings } from '@/libs/siteSettings'
import Image from 'next/image'
import LoadingComponent from '@/components/LoadingComponent'
import { Suspense } from 'react' 

export default async function page({params}) {
    const cssSection='sectionWrapper flex w-full h-full flex-none relative overflow-hidden'
    const {id}=await params
    const fetchData=await fetch(`${settings.url}/api/buildings/${id}`)
    const {building}=await fetchData.json()
    const data=building
    console.log('project page',building)
  return (
    <BuildPageComponent data={data}>
        {/* renders wrapper */}
        <section className={cssSection}>
          <ScrollerWrapper>
            {data?.renders?.map((i,index)=>
              <div key={index} className='flex brightness-90 relative w-full h-full flex-none'>
                <Image priority src={i?.url} className='object-cover flex-none' alt="view" fill/>
              </div>
            )}
          </ScrollerWrapper>
        </section>
        
        {/* drawings wrapper */}
        <section className={cssSection}>
          <ScrollerWrapper>
            {data?.drawings?.map((i,index)=>
              <div key={index} className='flex brightness-90 relative w-full h-full flex-none'>
                <Image priority src={i?.url} className='object-cover flex-none' alt="view" fill/>
              </div>
            )}
          </ScrollerWrapper>
        </section>

        {/* experience wrapper */}
        <section className={cssSection}>
          <Suspense fallback={<LoadingComponent/>}>
            <ExperienceWrapper data={data}/>
          </Suspense>
        </section>
    </BuildPageComponent>
  )
}
