'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  HiOfficeBuilding, 
  HiCalendar, 
  HiS<PERSON>ch, 
  Hi<PERSON>ilter,
  HiSortAscending,
  HiSortDescending,
  HiEye
} from 'react-icons/hi'
import { IoCarOutline, IoBedOutline } from "react-icons/io5"
import { LuBath } from "react-icons/lu"
import { SiLevelsdotfyi } from "react-icons/si"

export default function ClientProjects({ userRole }) {
  const [projects, setProjects] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('createdAt')
  const [sortOrder, setSortOrder] = useState('desc')
  const [buildingTypeFilter, setBuildingTypeFilter] = useState('')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 6,
    total: 0,
    totalPages: 0
  })

  // Only show for client users
  if (userRole !== 'client') {
    return null
  }

  const fetchProjects = async (page = 1) => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        sortOrder,
        ...(searchTerm && { search: searchTerm }),
        ...(buildingTypeFilter && { buildingType: buildingTypeFilter })
      })

      const response = await fetch(`/api/client/projects?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch projects')
      }

      const data = await response.json()
      setProjects(data.buildings)
      setPagination(data.pagination)
      setError('')
    } catch (err) {
      setError(err.message)
      setProjects([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProjects()
  }, [sortBy, sortOrder, buildingTypeFilter])

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchProjects(1)
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchTerm])

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('desc')
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      className="bg-white border border-neutral-200 rounded-lg p-8 shadow-sm"
    >
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <HiOfficeBuilding className="w-6 h-6 text-neutral-600 mr-3" />
          <h2 className="chaumet-heading text-2xl text-neutral-900">My Projects</h2>
        </div>
        <div className="text-sm text-neutral-500 font-light">
          {pagination.total} project{pagination.total !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Search and Filter Controls */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1 relative">
          <HiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-neutral-900 focus:border-transparent font-light"
          />
        </div>
        
        <select
          value={buildingTypeFilter}
          onChange={(e) => setBuildingTypeFilter(e.target.value)}
          className="px-4 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-neutral-900 focus:border-transparent font-light"
        >
          <option value="">All Types</option>
          <option value="multi-storey">Multi-Storey</option>
          <option value="single-storey">Single-Storey</option>
          <option value="multi-residence">Multi-Residence</option>
        </select>
      </div>

      {/* Sort Controls */}
      <div className="flex flex-wrap gap-2 mb-6">
        {[
          { field: 'createdAt', label: 'Date Created' },
          { field: 'updatedAt', label: 'Date Updated' },
          { field: 'buildingTitle', label: 'Title' },
          { field: 'buildingType', label: 'Type' }
        ].map(({ field, label }) => (
          <button
            key={field}
            onClick={() => handleSort(field)}
            className={`flex items-center px-3 py-1 rounded-lg text-sm font-light transition-colors ${
              sortBy === field
                ? 'bg-neutral-900 text-white'
                : 'bg-neutral-100 text-neutral-600 hover:bg-neutral-200'
            }`}
          >
            {label}
            {sortBy === field && (
              sortOrder === 'asc' ? 
                <HiSortAscending className="ml-1 w-3 h-3" /> : 
                <HiSortDescending className="ml-1 w-3 h-3" />
            )}
          </button>
        ))}
      </div>

      {/* Error State */}
      {error && (
        <div className="text-red-600 text-sm mb-4 p-3 bg-red-50 rounded-lg">
          {error}
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-neutral-900"></div>
        </div>
      )}

      {/* Projects Grid */}
      {!loading && projects.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {projects.map((project) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              className="border border-neutral-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-light text-neutral-900 mb-1">
                    {project.buildingTitle}
                  </h3>
                  <p className="text-sm text-neutral-500 font-light tracking-wider uppercase">
                    {project.buildingType}
                  </p>
                </div>
                <Link
                  href={`/projects/${project.id}`}
                  className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
                >
                  <HiEye className="w-4 h-4 mr-1" />
                  <span className="text-xs font-light">View</span>
                </Link>
              </div>

              {/* Building Summary */}
              {project.buildingSummary && (
                <div className="grid grid-cols-4 gap-2 mb-4 p-3 bg-neutral-50 rounded-lg">
                  <div className="text-center">
                    <IoBedOutline className="w-4 h-4 mx-auto text-neutral-500 mb-1" />
                    <span className="text-xs text-neutral-600">{project.buildingSummary.beds}</span>
                  </div>
                  <div className="text-center">
                    <LuBath className="w-4 h-4 mx-auto text-neutral-500 mb-1" />
                    <span className="text-xs text-neutral-600">{project.buildingSummary.baths}</span>
                  </div>
                  <div className="text-center">
                    <IoCarOutline className="w-4 h-4 mx-auto text-neutral-500 mb-1" />
                    <span className="text-xs text-neutral-600">{project.buildingSummary.cars}</span>
                  </div>
                  <div className="text-center">
                    <SiLevelsdotfyi className="w-4 h-4 mx-auto text-neutral-500 mb-1" />
                    <span className="text-xs text-neutral-600">{project.buildingSummary.levels}</span>
                  </div>
                </div>
              )}

              <div className="flex items-center text-xs text-neutral-500">
                <HiCalendar className="w-3 h-3 mr-1" />
                <span>Created {formatDate(project.createdAt)}</span>
                {project.updatedAt !== project.createdAt && (
                  <span className="ml-2">• Updated {formatDate(project.updatedAt)}</span>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!loading && projects.length === 0 && (
        <div className="text-center py-12">
          <HiOfficeBuilding className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
          <h3 className="text-lg font-light text-neutral-600 mb-2">No Projects Assigned</h3>
          <p className="text-neutral-500 font-light">
            You don't have any projects assigned yet. Contact your administrator for project access.
          </p>
        </div>
      )}

      {/* Pagination */}
      {!loading && pagination.totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2">
          <button
            onClick={() => fetchProjects(pagination.page - 1)}
            disabled={!pagination.hasPrev}
            className="px-3 py-1 text-sm border border-neutral-200 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-neutral-50 transition-colors"
          >
            Previous
          </button>
          <span className="text-sm text-neutral-600">
            Page {pagination.page} of {pagination.totalPages}
          </span>
          <button
            onClick={() => fetchProjects(pagination.page + 1)}
            disabled={!pagination.hasNext}
            className="px-3 py-1 text-sm border border-neutral-200 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-neutral-50 transition-colors"
          >
            Next
          </button>
        </div>
      )}
    </motion.div>
  )
}
