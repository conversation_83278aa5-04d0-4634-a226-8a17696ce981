export default function Loading() {
  return (
    <div className="min-h-screen bg-neutral-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Skeleton */}
        <div className="mb-8">
          {/* Breadcrumb Skeleton */}
          <div className="flex items-center space-x-2 text-sm mb-4">
            <div className="h-4 w-24 bg-neutral-200 rounded animate-pulse"></div>
            <span>/</span>
            <div className="h-4 w-16 bg-neutral-200 rounded animate-pulse"></div>
          </div>

          {/* Page Header Skeleton */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-10 w-32 bg-neutral-200 rounded animate-pulse"></div>
              <div>
                <div className="h-8 w-48 bg-neutral-200 rounded animate-pulse mb-2"></div>
                <div className="h-4 w-64 bg-neutral-200 rounded animate-pulse"></div>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-2">
              <div className="h-6 w-6 bg-neutral-200 rounded animate-pulse"></div>
              <div className="h-4 w-20 bg-neutral-200 rounded animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Content Skeleton */}
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm">
          <div className="p-6">
            {/* Search and Filter Skeleton */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="h-10 w-full bg-neutral-200 rounded animate-pulse"></div>
              </div>
              <div className="flex gap-2">
                <div className="h-10 w-32 bg-neutral-200 rounded animate-pulse"></div>
                <div className="h-10 w-24 bg-neutral-200 rounded animate-pulse"></div>
              </div>
            </div>

            {/* Table Skeleton */}
            <div className="overflow-x-auto">
              <div className="min-w-full">
                {/* Table Header */}
                <div className="bg-neutral-50 border-b border-neutral-200 p-4">
                  <div className="flex space-x-6">
                    <div className="h-4 w-4 bg-neutral-200 rounded animate-pulse"></div>
                    <div className="h-4 w-20 bg-neutral-200 rounded animate-pulse"></div>
                    <div className="h-4 w-32 bg-neutral-200 rounded animate-pulse"></div>
                    <div className="h-4 w-24 bg-neutral-200 rounded animate-pulse"></div>
                    <div className="h-4 w-16 bg-neutral-200 rounded animate-pulse"></div>
                    <div className="h-4 w-20 bg-neutral-200 rounded animate-pulse"></div>
                    <div className="h-4 w-24 bg-neutral-200 rounded animate-pulse"></div>
                    <div className="h-4 w-16 bg-neutral-200 rounded animate-pulse"></div>
                  </div>
                </div>

                {/* Table Rows */}
                {[...Array(5)].map((_, index) => (
                  <div key={index} className="border-b border-neutral-200 p-4">
                    <div className="flex space-x-6 items-center">
                      <div className="h-4 w-4 bg-neutral-200 rounded animate-pulse"></div>
                      <div className="h-4 w-20 bg-neutral-200 rounded animate-pulse"></div>
                      <div className="h-4 w-32 bg-neutral-200 rounded animate-pulse"></div>
                      <div className="h-4 w-24 bg-neutral-200 rounded animate-pulse"></div>
                      <div className="h-6 w-16 bg-neutral-200 rounded-full animate-pulse"></div>
                      <div className="h-4 w-20 bg-neutral-200 rounded animate-pulse"></div>
                      <div className="h-4 w-24 bg-neutral-200 rounded animate-pulse"></div>
                      <div className="flex space-x-2">
                        <div className="h-4 w-4 bg-neutral-200 rounded animate-pulse"></div>
                        <div className="h-4 w-4 bg-neutral-200 rounded animate-pulse"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Pagination Skeleton */}
            <div className="flex items-center justify-between mt-6 pt-4 border-t border-neutral-200">
              <div className="h-4 w-32 bg-neutral-200 rounded animate-pulse"></div>
              <div className="flex space-x-2">
                <div className="h-8 w-8 bg-neutral-200 rounded animate-pulse"></div>
                <div className="h-8 w-8 bg-neutral-200 rounded animate-pulse"></div>
                <div className="h-8 w-8 bg-neutral-200 rounded animate-pulse"></div>
                <div className="h-8 w-8 bg-neutral-200 rounded animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
