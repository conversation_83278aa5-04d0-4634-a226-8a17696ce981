export default function Loading() {
  return (
    <div className="min-h-screen bg-neutral-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Skeleton */}
        <div className="mb-8">
          {/* Breadcrumb Skeleton */}
          <div className="flex items-center space-x-2 text-sm mb-4">
            <div className="h-4 w-24 bg-neutral-200 rounded animate-pulse"></div>
            <span>/</span>
            <div className="h-4 w-16 bg-neutral-200 rounded animate-pulse"></div>
            <span>/</span>
            <div className="h-4 w-20 bg-neutral-200 rounded animate-pulse"></div>
          </div>

          {/* Page Header Skeleton */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-10 w-32 bg-neutral-200 rounded animate-pulse"></div>
              <div>
                <div className="h-8 w-48 bg-neutral-200 rounded animate-pulse mb-2"></div>
                <div className="h-4 w-64 bg-neutral-200 rounded animate-pulse"></div>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-2">
              <div className="h-6 w-6 bg-neutral-200 rounded animate-pulse"></div>
              <div className="h-4 w-20 bg-neutral-200 rounded animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* User Details Form Skeleton */}
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm">
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information Skeleton */}
              <div className="space-y-6">
                <div className="h-6 w-32 bg-neutral-200 rounded animate-pulse mb-4"></div>
                
                {/* Form Fields */}
                {[...Array(3)].map((_, index) => (
                  <div key={index}>
                    <div className="h-4 w-20 bg-neutral-200 rounded animate-pulse mb-2"></div>
                    <div className="h-10 w-full bg-neutral-200 rounded animate-pulse"></div>
                  </div>
                ))}
              </div>

              {/* Additional Information Skeleton */}
              <div className="space-y-6">
                <div className="h-6 w-40 bg-neutral-200 rounded animate-pulse mb-4"></div>
                
                {/* Form Fields */}
                {[...Array(4)].map((_, index) => (
                  <div key={index}>
                    <div className="h-4 w-24 bg-neutral-200 rounded animate-pulse mb-2"></div>
                    <div className="h-10 w-full bg-neutral-200 rounded animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>

            {/* Project Assignment Skeleton */}
            <div className="mt-8 pt-6 border-t border-neutral-200">
              <div className="h-6 w-36 bg-neutral-200 rounded animate-pulse mb-4"></div>
              <div className="space-y-4">
                <div className="h-10 w-full bg-neutral-200 rounded animate-pulse"></div>
                <div className="flex flex-wrap gap-2">
                  {[...Array(3)].map((_, index) => (
                    <div key={index} className="h-8 w-32 bg-neutral-200 rounded-full animate-pulse"></div>
                  ))}
                </div>
              </div>
            </div>

            {/* Action Buttons Skeleton */}
            <div className="mt-8 pt-6 border-t border-neutral-200 flex justify-end space-x-4">
              <div className="h-10 w-20 bg-neutral-200 rounded animate-pulse"></div>
              <div className="h-10 w-32 bg-neutral-200 rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
