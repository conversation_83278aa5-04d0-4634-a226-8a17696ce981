import { useState, useEffect } from 'react'
import { FiPlus, FiX, FiSearch, FiCheck, FiAlertCircle } from 'react-icons/fi'

export default function ProjectAssignment({ 
  userId, 
  currentProjects = [], 
  onProjectsUpdate,
  isEditing = false 
}) {
  const [availableBuildings, setAvailableBuildings] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [showDropdown, setShowDropdown] = useState(false)
  const [selectedProjects, setSelectedProjects] = useState(currentProjects)
  const [updating, setUpdating] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')
  const [recentlyAdded, setRecentlyAdded] = useState(null)
  const [recentlyRemoved, setRecentlyRemoved] = useState(null)

  // Fetch available buildings for assignment
  const fetchAvailableBuildings = async (search = '') => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (search) params.append('search', search)

      const response = await fetch(`/api/admin/buildings/assignable?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch assignable buildings')
      }

      const data = await response.json()
      setAvailableBuildings(data.buildings || [])
      setError('')
    } catch (err) {
      setError(err.message)
      setAvailableBuildings([])
    } finally {
      setLoading(false)
    }
  }

  // Load buildings on component mount
  useEffect(() => {
    if (isEditing) {
      fetchAvailableBuildings()
    }
  }, [isEditing])

  // Handle search with debouncing
  useEffect(() => {
    if (!isEditing) return

    const timeoutId = setTimeout(() => {
      fetchAvailableBuildings(searchTerm)
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchTerm, isEditing])

  // Add project to selection
  const handleAddProject = async (building) => {
    if (selectedProjects.includes(building.id)) {
      return // Prevent duplicates
    }

    const newProjects = [...selectedProjects, building.id]
    setSelectedProjects(newProjects)
    setShowDropdown(false)
    setSearchTerm('')
    setRecentlyAdded(building.id)

    // Update user projects immediately
    await updateUserProjects(newProjects, `Added "${building.projectTitle}"`)

    // Clear the recently added indicator after animation
    setTimeout(() => setRecentlyAdded(null), 2000)
  }

  // Remove project from selection
  const handleRemoveProject = async (projectId) => {
    const building = getBuildingInfo(projectId)
    const newProjects = selectedProjects.filter(id => id !== projectId)
    setSelectedProjects(newProjects)
    setRecentlyRemoved(projectId)

    // Update user projects immediately
    await updateUserProjects(newProjects, `Removed "${building?.projectTitle || 'project'}"`)

    // Clear the recently removed indicator after animation
    setTimeout(() => setRecentlyRemoved(null), 2000)
  }

  // Update user projects via API
  const updateUserProjects = async (projects, successMsg = 'Projects updated') => {
    try {
      setUpdating(true)
      setError('')
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ projects }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update user projects')
      }

      // Show success message
      setSuccessMessage(successMsg)
      setTimeout(() => setSuccessMessage(''), 3000)

      // Notify parent component of the update
      if (onProjectsUpdate) {
        onProjectsUpdate(projects)
      }
    } catch (err) {
      setError(err.message)
      // Revert the local state on error
      setSelectedProjects(currentProjects)
    } finally {
      setUpdating(false)
    }
  }

  // Get building info for display
  const getBuildingInfo = (projectId) => {
    return availableBuildings.find(building => building.id === projectId)
  }

  // Filter available buildings (exclude already selected)
  const filteredBuildings = availableBuildings.filter(building => 
    !selectedProjects.includes(building.id)
  )

  if (!isEditing) {
    // Display mode - show current projects
    return (
      <div className="text-sm font-light text-neutral-900">
        {currentProjects.length > 0 ? (
          <div className="flex flex-wrap gap-1">
            {currentProjects.map((projectId, index) => {
              const building = getBuildingInfo(projectId)
              return (
                <span 
                  key={projectId}
                  className="bg-neutral-100 text-neutral-800 px-2 py-1 rounded-full text-xs font-light"
                  title={building?.label || projectId}
                >
                  {building?.projectTitle || `Project ${index + 1}`}
                </span>
              )
            })}
          </div>
        ) : (
          <span className="text-neutral-400 font-light">No projects assigned</span>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {/* Success Message */}
      {successMessage && (
        <div className="flex items-center gap-2 text-green-600 text-xs font-light mb-2 animate-fade-in">
          <FiCheck className="h-3 w-3" />
          {successMessage}
        </div>
      )}

      {/* Current Projects Display */}
      {selectedProjects.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-2">
          {selectedProjects.map((projectId) => {
            const building = getBuildingInfo(projectId)
            const isRecentlyAdded = recentlyAdded === projectId
            const isRecentlyRemoved = recentlyRemoved === projectId

            return (
              <div
                key={projectId}
                className={`bg-neutral-100 text-neutral-800 px-2 py-1 rounded-full text-xs font-light flex items-center gap-1 transition-all duration-300 ${
                  isRecentlyAdded ? 'animate-pulse bg-green-100 text-green-800' : ''
                } ${
                  isRecentlyRemoved ? 'animate-pulse bg-red-100 text-red-800' : ''
                }`}
              >
                <span title={building?.label || projectId}>
                  {building?.projectTitle || 'Unknown Project'}
                </span>
                <button
                  onClick={() => handleRemoveProject(projectId)}
                  className="text-neutral-500 hover:text-red-600 transition-colors"
                  disabled={updating}
                >
                  <FiX className="h-3 w-3" />
                </button>
              </div>
            )
          })}
        </div>
      )}

      {/* Projects Count Display */}
      {selectedProjects.length > 0 && (
        <div className="text-xs text-neutral-500 font-light mb-2">
          {selectedProjects.length} project{selectedProjects.length !== 1 ? 's' : ''} assigned
        </div>
      )}

      {/* Add Project Dropdown */}
      <div className="relative">
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search projects to assign..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onFocus={() => setShowDropdown(true)}
              className="chaumet-input pl-10 pr-4 py-2 text-sm"
              disabled={updating}
            />
          </div>
          {updating && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-neutral-600"></div>
          )}
        </div>

        {/* Dropdown */}
        {showDropdown && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-neutral-200 rounded-lg max-h-60 overflow-y-auto">
            {loading ? (
              <div className="p-3 text-center text-neutral-500 font-light">
                Loading projects...
              </div>
            ) : filteredBuildings.length > 0 ? (
              filteredBuildings.map((building) => (
                <button
                  key={building.id}
                  onClick={() => handleAddProject(building)}
                  className="w-full text-left px-3 py-2 hover:bg-neutral-50 transition-colors border-b border-neutral-100 last:border-b-0"
                  disabled={updating}
                >
                  <div className="font-light text-neutral-900 text-sm">
                    {building.label}
                  </div>
                  <div className="text-xs text-neutral-500 font-light">
                    {building.buildingType}
                  </div>
                </button>
              ))
            ) : (
              <div className="p-3 text-center text-neutral-500 font-light">
                {searchTerm ? 'No matching projects found' : 'No available projects'}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="flex items-center gap-2 text-red-600 text-xs font-light animate-fade-in">
          <FiAlertCircle className="h-3 w-3" />
          {error}
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div 
          className="fixed inset-0 z-5"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  )
}
