import PageWrapper from '@/components/PageWrapper'
import { settings } from '@/libs/siteSettings'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'

export default async function page() {
  const res=await fetch(`${settings.url}/api/buildings`)
  const {buildings} = await res.json()

  return (
    <PageWrapper>
      <div className='flex w-full flex-col gap-8 py-8'>
        {/* Header Section */}
        
        <div className='overflow-y-auto'>
          <div className="text-center mb-8">
            <h1 className='chaumet-heading text-3xl md:text-5xl mb-4 text-neutral-900'>
              Our <span className="font-light">Projects</span>
            </h1>
            <div className="chaumet-divider w-16 mx-auto" />
            <p className="text-neutral-600 font-light tracking-wide mt-4 max-w-2xl mx-auto">
              Explore our portfolio of architectural visualizations, where precision meets artistry
            </p>
          </div>

          {/* Projects Grid */}
          <div className='grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 w-full'>
            {buildings.map(project =>
              <div
                key={project?._id}
                className='group flex flex-col bg-white border border-neutral-100 hover:border-neutral-200 transition-all duration-500 hover:shadow-lg'
              >
                <div className='relative w-full aspect-[4/3] overflow-hidden'>
                  <Image
                    src={project?.renders?.[0]?.url}
                    alt={project?.buildingTitle || 'Project'}
                    fill
                    className='object-cover transition-transform duration-700 group-hover:scale-105'
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-500" />
                </div>

                <div className='p-6 flex flex-col flex-grow'>
                  <h2 className='text-xl font-light text-neutral-900 mb-2 tracking-wide group-hover:text-neutral-700 transition-colors duration-300'>
                    {project?.buildingTitle}
                  </h2>
                  <p className='text-sm text-neutral-500 font-light tracking-wider uppercase'>
                    {project?.buildingType}
                  </p>

                  {/* Subtle hover indicator */}
                  <Link href={`/projects/${project?._id}`} className="mt-4 pt-4 border-t border-neutral-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <span className="text-xs text-neutral-400 font-light tracking-widest uppercase">
                      View Project
                    </span>
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </PageWrapper>
  )
}
