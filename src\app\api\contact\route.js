// Force Node.js runtime for email functionality
export const runtime = 'nodejs'

import { NextResponse } from 'next/server'
import nodemailer from 'nodemailer'
import { sendWhatsAppMessage, formatContactMessage } from '@/libs/whatsappService'

// Email sending function
async function sendEmail(name, email, phone, message) {
  try {
    // Create transporter using environment variables
    const transporter = nodemailer.createTransporter({
      host: "smtp.hostinger.com",
      port: 465,
      secure: true,
      auth: {
        user: "<EMAIL>",
        pass: process.env.EMAIL_PASSWORD,
      },
    })

    // Create HTML email template
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>New Contact Form Submission</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #1a1a1a; color: white; padding: 20px; text-align: center; }
          .content { background-color: #f9f9f9; padding: 20px; }
          .field { margin-bottom: 15px; }
          .label { font-weight: bold; color: #555; }
          .value { margin-top: 5px; padding: 10px; background-color: white; border-left: 4px solid #1a1a1a; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>New Contact Form Submission</h1>
            <p>Luyari Architectural Visualization</p>
          </div>
          <div class="content">
            <div class="field">
              <div class="label">Name:</div>
              <div class="value">${name}</div>
            </div>
            <div class="field">
              <div class="label">Email:</div>
              <div class="value">${email}</div>
            </div>
            <div class="field">
              <div class="label">Phone:</div>
              <div class="value">${phone || 'Not provided'}</div>
            </div>
            <div class="field">
              <div class="label">Message:</div>
              <div class="value">${message}</div>
            </div>
          </div>
          <div class="footer">
            <p>This message was sent from the contact form on luyari.com</p>
            <p>Please respond to the customer at: ${email}</p>
          </div>
        </div>
      </body>
      </html>
    `

    // Send email
    await transporter.sendMail({
      from: "<EMAIL>",
      to: "<EMAIL>",
      subject: `New Contact Form Submission from ${name}`,
      html: htmlContent,
      replyTo: email
    })

    return {
      success: true,
      message: 'Email sent successfully'
    }
  } catch (error) {
    console.error('Error sending email:', error)
    return {
      success: false,
      error: 'Failed to send email'
    }
  }
}

export async function POST(request) {
  try {
    const { name, email, phone, message } = await request.json()

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: 'Missing required fields: name, email, and message are required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate message length
    if (message.length < 10) {
      return NextResponse.json(
        { error: 'Message must be at least 10 characters long' },
        { status: 400 }
      )
    }

    // Send email
    const emailResult = await sendEmail(name, email, phone, message)

    // Send WhatsApp message
    const whatsappMessage = formatContactMessage({ name, email, phone, message })
    const whatsappResult = await sendWhatsAppMessage('26774308319', whatsappMessage)

    // Check results
    const emailSuccess = emailResult.success
    const whatsappSuccess = whatsappResult.success

    if (emailSuccess && whatsappSuccess) {
      return NextResponse.json({
        success: true,
        message: 'Message sent successfully via email and WhatsApp',
        details: {
          email: emailResult.message,
          whatsapp: whatsappResult.message,
          whatsappUrl: whatsappResult.url
        }
      })
    } else if (emailSuccess) {
      return NextResponse.json({
        success: true,
        message: 'Message sent via email. WhatsApp delivery failed.',
        details: {
          email: emailResult.message,
          whatsapp: whatsappResult.error
        }
      })
    } else if (whatsappSuccess) {
      return NextResponse.json({
        success: true,
        message: 'Message prepared for WhatsApp. Email delivery failed.',
        details: {
          email: emailResult.error,
          whatsapp: whatsappResult.message,
          whatsappUrl: whatsappResult.url
        }
      })
    } else {
      return NextResponse.json(
        { 
          error: 'Failed to send message via both email and WhatsApp',
          details: {
            email: emailResult.error,
            whatsapp: whatsappResult.error
          }
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error in contact API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
