'use client'

import { useSession } from 'next-auth/react'
import { useRouter, useParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { FiUser, FiArrowLeft, FiSave, FiX, FiMail, FiPhone, FiCalendar, FiShield } from 'react-icons/fi'
import Link from 'next/link'
import ProjectAssignment from '@/components/admin/ProjectAssignment'

export default function UserDetailPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const userId = params.id

  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [successMessage, setSuccessMessage] = useState('')
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    phone: '',
    firstName: '',
    lastName: '',
    role: 'user',
    projects: []
  })

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user?.role !== 'admin') {
      router.push('/auth/signin')
      return
    }

    fetchUser()
  }, [session, status, router, userId])

  const fetchUser = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/users/${userId}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch user')
      }

      const userData = await response.json()
      setUser(userData)
      setFormData({
        username: userData.username || '',
        email: userData.email || '',
        phone: userData.phone || '',
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        role: userData.role || 'user',
        projects: userData.projects || []
      })
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      setError('')

      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update user')
      }

      const updatedUser = await response.json()
      setUser(updatedUser)
      setSuccessMessage('User updated successfully')
      setTimeout(() => setSuccessMessage(''), 3000)
    } catch (err) {
      setError(err.message)
    } finally {
      setSaving(false)
    }
  }

  const handleProjectsUpdate = (projects) => {
    setFormData(prev => ({
      ...prev,
      projects
    }))
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-neutral-600"></div>
      </div>
    )
  }

  if (!session || session.user?.role !== 'admin') {
    return null
  }

  if (error && !user) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">{error}</div>
          <Link href="/admin/users" className="chaumet-button border-neutral-900 text-neutral-900">
            Back to Users
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-neutral-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm font-light text-neutral-600 mb-4">
            <Link 
              href="/admin" 
              className="hover:text-neutral-900 transition-colors duration-300"
            >
              Admin Dashboard
            </Link>
            <span>/</span>
            <Link 
              href="/admin/users" 
              className="hover:text-neutral-900 transition-colors duration-300"
            >
              Users
            </Link>
            <span>/</span>
            <span className="text-neutral-900">{user?.username || 'User Details'}</span>
          </nav>

          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href="/admin/users"
                className="chaumet-button border-neutral-300 text-neutral-600 group hover:border-neutral-900 hover:text-neutral-900"
              >
                <FiArrowLeft className="h-4 w-4 mr-2" />
                Back to Users
              </Link>
              <div>
                <h1 className="chaumet-heading text-3xl md:text-4xl text-neutral-900 mb-2">
                  {user?.username || 'User Details'}
                </h1>
                <p className="text-neutral-600 font-light tracking-wide">
                  Edit user information and manage permissions
                </p>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-2 text-neutral-600">
              <FiUser className="h-6 w-6" />
              <span className="font-light">User Profile</span>
            </div>
          </div>
        </motion.div>

        {/* Success Message */}
        {successMessage && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6"
          >
            {successMessage}
          </motion.div>
        )}

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6"
          >
            {error}
          </motion.div>
        )}

        {/* User Details Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-white rounded-lg border border-neutral-200 shadow-sm"
        >
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-6">
                <h3 className="chaumet-heading text-xl text-neutral-900 mb-4">Basic Information</h3>
                
                <div>
                  <label className="block text-sm font-light text-neutral-700 mb-2">
                    <FiUser className="inline h-4 w-4 mr-2" />
                    Username
                  </label>
                  <input
                    type="text"
                    value={formData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    className="chaumet-input"
                    placeholder="Enter username"
                  />
                </div>

                <div>
                  <label className="block text-sm font-light text-neutral-700 mb-2">
                    <FiMail className="inline h-4 w-4 mr-2" />
                    Email
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="chaumet-input"
                    placeholder="Enter email address"
                    disabled
                  />
                  <p className="text-xs text-neutral-500 mt-1">Email cannot be changed</p>
                </div>

                <div>
                  <label className="block text-sm font-light text-neutral-700 mb-2">
                    <FiPhone className="inline h-4 w-4 mr-2" />
                    Phone
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="chaumet-input"
                    placeholder="Enter phone number"
                  />
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-6">
                <h3 className="chaumet-heading text-xl text-neutral-900 mb-4">Additional Information</h3>
                
                <div>
                  <label className="block text-sm font-light text-neutral-700 mb-2">
                    First Name
                  </label>
                  <input
                    type="text"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    className="chaumet-input"
                    placeholder="Enter first name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-light text-neutral-700 mb-2">
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    className="chaumet-input"
                    placeholder="Enter last name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-light text-neutral-700 mb-2">
                    <FiShield className="inline h-4 w-4 mr-2" />
                    Role
                  </label>
                  <select
                    value={formData.role}
                    onChange={(e) => handleInputChange('role', e.target.value)}
                    className="chaumet-select"
                  >
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                    <option value="client">Client</option>
                  </select>
                </div>

                {user && (
                  <div>
                    <label className="block text-sm font-light text-neutral-700 mb-2">
                      <FiCalendar className="inline h-4 w-4 mr-2" />
                      Date Created
                    </label>
                    <div className="chaumet-input bg-neutral-50 text-neutral-600">
                      {new Date(user.dateCreated).toLocaleDateString()}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Project Assignment for Client Users */}
            {formData.role === 'client' && (
              <div className="mt-8 pt-6 border-t border-neutral-200">
                <h3 className="chaumet-heading text-xl text-neutral-900 mb-4">Project Assignments</h3>
                <ProjectAssignment
                  userId={userId}
                  currentProjects={formData.projects}
                  onProjectsUpdate={handleProjectsUpdate}
                  isEditing={true}
                />
              </div>
            )}

            {/* Action Buttons */}
            <div className="mt-8 pt-6 border-t border-neutral-200 flex justify-end space-x-4">
              <Link
                href="/admin/users"
                className="chaumet-button border-neutral-300 text-neutral-600 hover:border-neutral-900 hover:text-neutral-900"
              >
                <FiX className="h-4 w-4 mr-2" />
                Cancel
              </Link>
              <button
                onClick={handleSave}
                disabled={saving}
                className="chaumet-button border-neutral-900 text-neutral-900 group"
              >
                <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center">
                  <FiSave className="h-4 w-4 mr-2" />
                  {saving ? 'Saving...' : 'Save Changes'}
                </span>
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
